use std::io;

fn main() {
    let riddle = "I am the beginning of the end, and the end of time and space. \
I am essential to creation, and I surround every place.\nWhat am I?";
    let correct_answer = "the letter e";
    let mut trials = 0;

    loop {
        println!("{}", riddle);

        let mut input = String::new();
        io::stdin()
            .read_line(&mut input)
            .expect("Failed to read input");

        let answer = input.trim().to_lowercase();
        trials += 1;

        if answer == correct_answer {
            println!("Number of trials: {}", trials);
            break;
        }
    }
}
