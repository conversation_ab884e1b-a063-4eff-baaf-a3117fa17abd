use std::io;

fn main() {
    let riddle = "I am the beginning of the end, and the end of time and space. I am essential to creation, and I surround every place. What am I?";
    let correct_answer = "e";
    let mut trials = 0;

    loop {
        println!("{}", riddle);

        let mut input = String::new();
        match io::stdin().read_line(&mut input) {
            Ok(0) => break, // EOF reached
            Ok(_) => {
                let answer = input.trim().to_lowercase();
                trials += 1;

                if answer == correct_answer {
                    println!("The letter e");
                    println!("Number of trials: {}", trials);
                    break;
                } else {
                    println!("I don't know");
                }
            }
            Err(_) => break, // Error reading input
        }
    }
}
